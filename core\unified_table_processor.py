#!/usr/bin/env python3
"""
Unified Table Processor Module

This module consolidates the functionality from table_processor.py and smart_table_processor.py
into a single, clean implementation using the Strategy pattern.

REFACTORED: Eliminates code duplication by merging:
- BasicTableProcessor (from table_processor.py)
- SmartTableProcessor (from smart_table_processor.py)
- Adds new ParallelTableProcessor for concurrent processing

Features:
- Strategy pattern for different processing modes
- Unified error handling using custom exceptions
- Comprehensive logging and progress reporting
- Automatic cleanup and validation
- Support for dry-run operations
"""

import os
import logging
import time
import datetime
import tempfile
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from pathlib import Path

# Import configuration and utility modules
from core.config_manager import ConfigManager
from core.backup_config import BackupConfig
from core.devo_client import DevoClient
from core.chunk_manager import ChunkManager
from core.storage_manager import StorageManager
from core.progress_reporter import ProgressReporter
from core.performance_monitor import PerformanceMonitor

# Import unified exceptions
from utils.exceptions import (
    BackupSystemError, ValidationError, StorageError,
    handle_errors, log_error_with_context
)

# Import validation
from utils.validation import ComprehensiveBackupValidator
from core.incremental_validator import IncrementalValidator

# Configure logging
logger = logging.getLogger(__name__)


class ProcessingStrategy(ABC):
    """
    Abstract base class for table processing strategies.

    This allows different processing approaches while maintaining
    a consistent interface.
    """

    @abstractmethod
    def process_tables(self, table_names: List[str], config: BackupConfig,
                      **options) -> Dict[str, Any]:
        """
        Process a list of tables using this strategy.

        Args:
            table_names: List of table names to process
            config: Backup configuration
            **options: Additional processing options

        Returns:
            Processing results dictionary
        """
        pass


class SmartProcessingStrategy(ProcessingStrategy):
    """
    Smart processing strategy with validation and optimization.

    This strategy includes:
    - Pre-backup validation
    - Sequential processing for reliability
    - Automatic cleanup
    - Skip empty tables
    - Enhanced progress reporting
    """

    def __init__(self):
        self.stats = {
            'total_tables': 0,
            'successful_tables': 0,
            'failed_tables': 0,
            'empty_tables_skipped': 0,
            'total_rows_backed_up': 0,
            'total_processing_time': 0
        }

    @handle_errors("smart_table_processing")
    def process_tables(self, table_names: List[str], config: BackupConfig,
                      skip_empty: bool = True, **options) -> Dict[str, Any]:
        """
        Process tables using smart strategy with validation.

        Args:
            table_names: List of table names to process
            config: Backup configuration
            skip_empty: Whether to skip empty tables
            **options: Additional options

        Returns:
            Processing results
        """
        start_time = time.time()
        self.stats['total_tables'] = len(table_names)

        logger.info(f"Starting smart processing of {len(table_names)} tables")

        # Initialize components
        devo_client = DevoClient()
        storage_manager = StorageManager(config.config_manager)
        chunk_manager = ChunkManager(config)
        progress_reporter = ProgressReporter(
            name="smart_table_processing",
            desc=f"Processing {len(table_names)} tables",
            total=len(table_names)
        )

        table_results = {}

        for i, table_name in enumerate(table_names):
            try:
                logger.info(f"Processing table {i+1}/{len(table_names)}: {table_name}")
                progress_reporter.update(0, f"Processing {table_name}")

                # Validate table has data (if skip_empty is True)
                if skip_empty and self._is_table_empty(table_name, devo_client, config):
                    logger.info(f"Skipping empty table: {table_name}")
                    self.stats['empty_tables_skipped'] += 1
                    table_results[table_name] = {
                        'status': 'skipped',
                        'reason': 'empty_table',
                        'rows_backed_up': 0
                    }
                    progress_reporter.update(1, f"Skipped {table_name} (empty)")
                    continue

                # Process the table
                result = self._process_single_table(
                    table_name, config, devo_client, storage_manager,
                    chunk_manager, progress_reporter
                )

                table_results[table_name] = result

                if result.get('status') == 'success':
                    self.stats['successful_tables'] += 1
                    self.stats['total_rows_backed_up'] += result.get('rows_backed_up', 0)
                    progress_reporter.update(1, f"Completed {table_name} ({result.get('rows_backed_up', 0)} rows)")
                else:
                    self.stats['failed_tables'] += 1
                    progress_reporter.update(1, f"Failed {table_name}")

                # Cleanup after each table to minimize storage usage
                self._cleanup_temp_files()

            except Exception as e:
                error_msg = f"Error processing table {table_name}: {str(e)}"
                logger.error(error_msg)
                log_error_with_context(e, f"table_processing_{table_name}")

                table_results[table_name] = {
                    'status': 'error',
                    'error': str(e),
                    'rows_backed_up': 0
                }
                self.stats['failed_tables'] += 1
                progress_reporter.update(1, f"Error {table_name}")

        # Calculate final statistics
        self.stats['total_processing_time'] = time.time() - start_time

        return {
            'status': 'success' if self.stats['failed_tables'] == 0 else 'partial',
            'summary': self.stats.copy(),
            'table_results': table_results,
            'processing_strategy': 'smart'
        }

    def _is_table_empty(self, table_name: str, devo_client: DevoClient,
                       config: BackupConfig) -> bool:
        """
        Check if a table is empty using incremental validation when possible.

        Args:
            table_name: Name of the table
            devo_client: Devo client instance
            config: Backup configuration

        Returns:
            True if table is empty, False otherwise
        """
        try:
            # Use incremental validator for optimized empty table detection
            if hasattr(config, 'use_incremental_validation') and config.use_incremental_validation:
                validator = IncrementalValidator(config)
                result = validator._validate_single_table(table_name)
                return not result.get('validation_result', {}).get('has_data', False)

            # Fallback to direct query
            # SECURITY FIX: Use secure query construction
            # Quick count query to check if table has data - table_name will be validated by DevoClient
            count_query = f"from {table_name} select count() as total"
            results = devo_client.execute_query(
                count_query,
                days=config.days,
                timeout=30,  # Short timeout for count queries
                table_name=table_name
            )

            if results and len(results) > 0:
                total_count = results[0].get('total', 0)
                return total_count == 0

            return True  # Assume empty if can't get count

        except Exception as e:
            logger.warning(f"Could not check if table {table_name} is empty: {str(e)}")
            return False  # Don't skip if we can't determine

    def _process_single_table(self, table_name: str, config: BackupConfig,
                            devo_client: DevoClient, storage_manager: StorageManager,
                            chunk_manager: ChunkManager, progress_reporter: ProgressReporter) -> Dict[str, Any]:
        """
        Process a single table.

        Args:
            table_name: Name of the table to process
            config: Backup configuration
            devo_client: Devo client instance
            storage_manager: Storage manager instance
            chunk_manager: Chunk manager instance
            progress_reporter: Progress reporter instance

        Returns:
            Processing result for the table
        """
        # SECURITY FIX: Use secure temporary directory creation
        output_dir = None
        try:
            # SECURITY FIX: Create secure temporary directory instead of predictable naming
            # This prevents race condition attacks and unauthorized access
            safe_table_name = table_name.replace('.', '_').replace('/', '_').replace('\\', '_')
            output_dir = tempfile.mkdtemp(prefix=f'tngd_{safe_table_name}_')
            logger.info(f"Created secure temporary directory: {output_dir}")

            # Build query and determine backup date
            if config.specific_date:
                date_str = config.specific_date.strftime('%Y-%m-%d')
                where_clause = f"where eventdate >= '{date_str} 00:00:00' and eventdate < '{date_str} 23:59:59'"
                backup_date = config.specific_date
            else:
                where_clause = f"where eventdate >= now() - {config.days} * day()"
                backup_date = datetime.datetime.now()

            # Extract data in chunks
            total_rows, total_chunks = chunk_manager.query_and_save_data(
                table_name=table_name,
                where_clause=where_clause,
                output_dir=output_dir,
                reporter=progress_reporter
            )

            if total_rows == 0:
                return {
                    'status': 'success',
                    'rows_backed_up': 0,
                    'message': 'No data found for specified date range'
                }

            # Generate OSS path using ConfigManager template
            # Format table name for file naming (replace dots with underscores)
            formatted_table_name = table_name.replace('.', '_')

            # Add timestamp to make filename unique
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            table_name_with_timestamp = f"{formatted_table_name}_{timestamp}"

            # Use ConfigManager to generate path according to configured template
            # This will create paths like: Devo/June/week 3/2025-06-19/my_app_tngd_waf_20250619_HHMMSS.tar.gz
            oss_path = config.config_manager.get_oss_path(
                table_name=table_name_with_timestamp,
                date=backup_date,
                algorithm='tar.gz'
            )

            # Compress and upload using the new StorageManager interface
            success, operation_details = storage_manager.compress_and_upload(
                output_dir, oss_path, verify_integrity=True
            )

            if not success:
                error_msg = operation_details.get('error', 'Unknown compression/upload error')
                raise StorageError(f"Failed to compress and upload data for table {table_name}: {error_msg}")

            return {
                'status': 'success',
                'rows_backed_up': total_rows,
                'chunks_processed': total_chunks,
                'oss_path': oss_path,
                'compressed_size_bytes': operation_details.get('upload_details', {}).get('file_size', 0),
                'compression_stats': operation_details.get('compression_stats', {}),
                'upload_details': operation_details.get('upload_details', {}),
                'checksum': operation_details.get('checksum', ''),
                'verified': operation_details.get('verified', False)
            }
        except (StorageError, ValidationError) as e:
            # Re-raise known BackupSystemError subclasses as is to preserve arguments
            logger.error(f"Error processing table {table_name}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Error processing table {table_name}: {str(e)}")
            raise BackupSystemError(f"Table processing failed: {str(e)}")
        finally:
            # SECURITY FIX: Ensure secure cleanup of temporary directory
            if output_dir and os.path.exists(output_dir):
                try:
                    import shutil
                    shutil.rmtree(output_dir)
                    logger.info(f"Cleaned up secure temporary directory: {output_dir}")
                except OSError as cleanup_error:
                    logger.warning(f"Failed to cleanup temporary directory {output_dir}: {cleanup_error}")

    def _cleanup_temp_files(self):
        """Clean up temporary files to minimize storage usage."""
        try:
            temp_dir = "temp"
            if os.path.exists(temp_dir):
                import shutil
                for item in os.listdir(temp_dir):
                    item_path = os.path.join(temp_dir, item)
                    if os.path.isdir(item_path):
                        shutil.rmtree(item_path)
                    else:
                        os.remove(item_path)
                logger.debug("Cleaned up temporary files")
        except Exception as e:
            logger.warning(f"Error cleaning up temp files: {str(e)}")


class BasicProcessingStrategy(ProcessingStrategy):
    """
    Basic processing strategy for simple, reliable processing.

    This strategy provides:
    - Simple sequential processing
    - Basic error handling
    - Minimal resource usage
    """

    @handle_errors("basic_table_processing")
    def process_tables(self, table_names: List[str], config: BackupConfig,
                      **options) -> Dict[str, Any]:
        """
        Process tables using basic strategy.

        Args:
            table_names: List of table names to process
            config: Backup configuration
            **options: Additional options

        Returns:
            Processing results
        """
        logger.info(f"Starting basic processing of {len(table_names)} tables")

        # Basic implementation - can be expanded as needed
        # For now, delegate to smart strategy but with simpler options
        smart_strategy = SmartProcessingStrategy()
        return smart_strategy.process_tables(table_names, config, skip_empty=False)


class ParallelProcessingStrategy(ProcessingStrategy):
    """
    Parallel processing strategy for high-performance backup operations.

    This strategy provides:
    - Selective parallelization with resource monitoring
    - Adaptive thread pool sizing
    - Independent table processing with isolation
    - Comprehensive error handling and recovery
    """

    @handle_errors("parallel_table_processing")
    def process_tables(self, table_names: List[str], config: BackupConfig,
                      **options) -> Dict[str, Any]:
        """
        Process tables using parallel strategy.

        Args:
            table_names: List of table names to process
            config: Backup configuration
            **options: Additional options

        Returns:
            Processing results
        """
        logger.info(f"Starting parallel processing of {len(table_names)} tables")

        # Import here to avoid circular imports
        from core.parallel_processor import ParallelTableProcessor

        # Create parallel processor
        parallel_processor = ParallelTableProcessor(config)

        # Process tables in parallel
        return parallel_processor.process_tables(table_names, **options)


class UnifiedTableProcessor:
    """
    Unified table processor that consolidates functionality from
    table_processor.py and smart_table_processor.py.

    Uses the Strategy pattern to support different processing approaches
    while eliminating code duplication.
    """

    def __init__(self, config: Optional[BackupConfig] = None,
                 strategy: str = 'smart'):
        """
        Initialize the unified table processor.

        Args:
            config: Backup configuration
            strategy: Processing strategy ('smart', 'basic', 'parallel')
        """
        self.config = config or BackupConfig()
        self.strategy = self._get_strategy(strategy)
        self.validator = ComprehensiveBackupValidator(self.config.config_manager)

    def _get_strategy(self, strategy_name: str) -> ProcessingStrategy:
        """
        Get the processing strategy instance.

        Args:
            strategy_name: Name of the strategy

        Returns:
            Strategy instance
        """
        strategies = {
            'smart': SmartProcessingStrategy(),
            'basic': BasicProcessingStrategy(),
            'parallel': ParallelProcessingStrategy(),
        }

        strategy = strategies.get(strategy_name.lower())
        if not strategy:
            logger.warning(f"Unknown strategy '{strategy_name}', using 'smart'")
            strategy = strategies['smart']

        return strategy

    @handle_errors("table_processing")
    def process_tables(self, table_names: List[str], **options) -> Dict[str, Any]:
        """
        Process tables using the configured strategy.

        Args:
            table_names: List of table names to process
            **options: Additional processing options

        Returns:
            Processing results
        """
        logger.info(f"Processing {len(table_names)} tables using {type(self.strategy).__name__}")

        # Process tables using the selected strategy
        result = self.strategy.process_tables(table_names, self.config, **options)

        # Validate results if requested
        if self.config.validate and result.get('status') != 'error':
            try:
                validation_result = self.validator.validate_backup_result(result)
                result['validation'] = validation_result
                logger.info(f"Backup validation completed: {validation_result.get('overall_status', 'unknown')}")
            except Exception as e:
                logger.warning(f"Backup validation failed: {str(e)}")
                result['validation'] = {'overall_status': 'error', 'error': str(e)}

        return result
